import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useForm } from 'react-hook-form';
import { apiRequest } from '@/lib/queryClient';
import { Skull } from 'lucide-react';

interface ContactFormValues {
  name: string;
  email: string;
  subject: string;
  message: string;
  consent: boolean;
}

export default function ContactSection() {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ContactFormValues>({
    defaultValues: {
      name: '',
      email: '',
      subject: '',
      message: '',
      consent: false
    }
  });

  const onSubmit = async (data: ContactFormValues) => {
    setIsSubmitting(true);

    try {
      await apiRequest('POST', '/api/contact', data);

      toast({
        title: "Message received",
        description: "We've received your message. We might reply... or not. Depends on our mood.",
        variant: "default",
      });

      form.reset();
    } catch (error) {
      toast({
        title: "Error sending message",
        description: "Your message was too boring for our servers to process. Try being more interesting.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="contact" className="py-16 relative">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-4xl mx-auto bg-card rounded-lg p-8 border border-muted relative overflow-hidden"
        >
          <div className="absolute top-0 right-0 w-64 h-64 bg-primary/10 rounded-full blur-[50px] -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-secondary/10 rounded-full blur-[50px] translate-y-1/2 -translate-x-1/2"></div>

          <div className="relative">
            <div className="text-center mb-8">
              <h2 className="font-heading text-4xl font-bold">
                <span className="text-primary neon-text">Contact Us</span>
                <span className="block text-sm text-secondary italic mt-1">(If You're Brave Enough)</span>
              </h2>
              <p className="text-muted-foreground mt-4 max-w-2xl mx-auto">
                Have a question, feedback, or just want to be judged by an AI? Drop us a message. We might reply. No promises.
              </p>
            </div>

            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name" className="block text-muted-foreground mb-2 font-mono text-sm">
                    NAME (OR ALIAS, WE DON'T JUDGE)
                  </Label>
                  <Input
                    id="name"
                    {...form.register('name', { required: true })}
                    className="w-full bg-background border border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition"
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="block text-muted-foreground mb-2 font-mono text-sm">
                    EMAIL (WE PROMISE NOT TO SPAM... MUCH)
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    {...form.register('email', { required: true })}
                    className="w-full bg-background border border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="subject" className="block text-muted-foreground mb-2 font-mono text-sm">
                  SUBJECT (MAKE IT INTERESTING)
                </Label>
                <Select
                  onValueChange={(value: string) => form.setValue('subject', value)}
                  defaultValue={form.getValues('subject')}
                >
                  <SelectTrigger className="w-full bg-background border border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition">
                    <SelectValue placeholder="Select a subject (or don't, see if I care)" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="feature">Feature Request (Dream Big)</SelectItem>
                    <SelectItem value="bug">Bug Report (It's Not Me, It's You)</SelectItem>
                    <SelectItem value="praise">Shameless Praise (We Like Those)</SelectItem>
                    <SelectItem value="complaint">Complaint (Prepare To Be Mocked)</SelectItem>
                    <SelectItem value="other">Other (Surprise Us)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="message" className="block text-muted-foreground mb-2 font-mono text-sm">
                  MESSAGE (TRY TO BE ORIGINAL)
                </Label>
                <Textarea
                  id="message"
                  rows={5}
                  {...form.register('message', { required: true })}
                  className="w-full bg-background border border-muted rounded-md p-3 text-foreground focus:border-primary focus:outline-none transition"
                />
              </div>

              <div className="flex items-center">
                <Checkbox
                  id="consent"
                  checked={form.watch('consent')}
                  onCheckedChange={(checked: boolean) => form.setValue('consent', checked === true)}
                  className="mr-2 accent-primary"
                />
                <Label htmlFor="consent" className="text-muted-foreground text-sm">
                  I consent to being roasted by an AI and having my data analyzed in ways I'll never fully understand.
                </Label>
              </div>

              <div className="text-center">
                <Button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-8 py-3 bg-primary text-background font-heading font-bold rounded-md hover:bg-primary/90 transition relative group"
                >
                  SUBMIT TO JUDGMENT
                  <span className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Skull className="w-5 h-5 text-background" />
                  </span>
                </Button>
              </div>
            </form>
          </div>
        </motion.div>
    </section>
  );
}
